<?xml version="1.0" encoding="utf-8"?>
<TpsData xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <Name>Vulkan headers</Name>
  <Location>/Engine/Source/Runtime/Core/Private/Android/</Location>
  <Date>2016-06-16T14:06:40.8388741-04:00</Date>
  <Function>Code snippets from vk_platform.h and vulkan.h to validate Vulkan driver availability.</Function>
  <Justification>For Android Vulkan driver detection</Justification>
  <Eula>http://www.apache.org/licenses/LICENSE-2.0</Eula>
  <RedistributeTo>
    <EndUserGroup>Licensees</EndUserGroup>
    <EndUserGroup>Git</EndUserGroup>
    <EndUserGroup>P4</EndUserGroup>
  </RedistributeTo>
  <LicenseFolder>/Engine/Source/ThirdParty/Licenses/VulkanHeaders_License.txt</LicenseFolder>
</TpsData>