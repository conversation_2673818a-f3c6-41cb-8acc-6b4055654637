// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "Internationalization/Text.h"

namespace UE::Insights
{

////////////////////////////////////////////////////////////////////////////////////////////////////

enum class EDialogResponse
{
	OK = 0,
	Cancel = 1
};

////////////////////////////////////////////////////////////////////////////////////////////////////

class TRACEINSIGHTSCORE_API FMessageDialogUtils
{
public:
	static EDialogResponse ShowChoiceDialog(const FText& Title, const FText& Content);
};

////////////////////////////////////////////////////////////////////////////////////////////////////

} // namespace UE::Insights