{"FileVersion": 3, "Version": 1, "VersionName": "0.1", "FriendlyName": "StateTree", "Description": "General purpose hierarchical state machine", "Category": "Gameplay", "CreatedBy": "Epic Games, Inc.", "CreatedByURL": "https://epicgames.com", "DocsURL": "", "MarketplaceURL": "", "SupportURL": "", "EnabledByDefault": false, "CanContainContent": true, "IsBetaVersion": false, "Installed": false, "Modules": [{"Name": "StateTreeModule", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON>efault"}, {"Name": "StateTreeTestSuite", "Type": "UncookedOnly", "LoadingPhase": "<PERSON><PERSON>efault"}, {"Name": "StateTreeEditorModule", "Type": "UncookedOnly", "LoadingPhase": "<PERSON><PERSON><PERSON>"}], "Plugins": [{"Name": "PropertyAccessEditor", "Enabled": true}]}