#usda 1.0 

def Shader "HwPtexTexture_1" (
    sdrMetadata = {
        token role = "texture"
        token isPtex = "1"
    }
)
{
    uniform token info:id = "HwPtexTexture_1"
    uniform token info:implementationSource = "sourceAsset"

    # Add a dummy sourceAsset attribute with sourceType="glslfx", so that 
    # an entry gets created for this deprecated shader in the registry.
    uniform asset info:glslfx:sourceAsset = @./empty.glslfx@

    token inputs:faceIndexPrimvar = "ptexFaceIndex" (
        sdrMetadata = {
            token primvarProperty = "1"
        }
    )

    token inputs:faceOffsetPrimvar = "ptexFaceOffset" (
        sdrMetadata = {
            token primvarProperty = "1"
        }
    )

    asset inputs:file = @@ (
        doc = "Asset path to the file containg the image data."
    )

    float inputs:frame (
        doc = "The frame offset for animated textures."
    )

    float inputs:textureMemory (
        doc = """Amount of memory used to store the texture (in Mb). A value of 
        zero specifies the native resolution."""
    )

    int inputs:faceIndex (
        doc = """The Ptex face index from which to sample. By default, this
        parameter will be bound to the ptexFaceIndex primvar."""
    )

    int inputs:faceOffset (
        doc = """The Ptex face offset to be applied to the face index. By
        default, this parameter will be bound to the ptexFaceOffset primvar."""
    )

    float outputs:r ( 
        doc = "Outputs the red channel."
        sdrMetadata = {
            token swizzle = "x"
        }
    )

    float outputs:g (
        doc = "Outputs the green channel."
        sdrMetadata = {
            token swizzle = "y"
        }
    )

    float outputs:b (
        doc = "Outputs the blue channnel."
        sdrMetadata = {
            token swizzle = "z"
        }
    )

    float outputs:a (
        doc = "Outputs the alpha channnel."
        sdrMetadata = {
            token swizzle = "w"
        }
    )

    float3 outputs:rgb (
        doc = "Outputs the red, green and blue channels."
        sdrMetadata = {
            token swizzle = "xyz"
        }
    )

    float4 outputs:rgba (
        doc = "Outputs all 4 channels (red, green, blue and alpha)."
        sdrMetadata = {
            token swizzle = "xyzw"
        }
    )
}

def Shader "HwUvTexture_1" (
    sdrMetadata = {
        token role = "texture"
        token primvars = "uv"
    }
)
{
    uniform token info:id = "HwUvTexture_1"
    uniform token info:implementationSource = "sourceAsset"

    # Add a dummy sourceAsset attribute with sourceType="glslfx", so that 
    # an entry gets created for this deprecated shader in the registry.
    uniform asset info:glslfx:sourceAsset = @./empty.glslfx@

    asset inputs:file = @@ (
        doc = "Asset path to the file containg the image data."
    )

    float inputs:frame (
        doc = "The frame offset for animated textures."
    )

    float inputs:textureMemory (
        doc = """Amount of memory used to store the texture (in Mb). A value of 
        zero specifies the native resolution."""
    )

    float2 inputs:uv (
        doc = "The uv coordinates at which to sample the texture."
    )

    token inputs:wrapS (
        allowedTokens = ["clamp", "repeat", "mirror", "black"]
        doc = "Specifies the wrap mode for this texture."
    )

    token inputs:wrapT (
        allowedTokens = ["clamp", "repeat", "mirror", "black"]
        doc = "Specifies the wrap mode for this texture."
    )

    token inputs:minFilter (
        allowedTokens = ["nearest", "linear",
                         "linearMipmapLinear", "linearMipmapNearest",
                         "nearestMipmapLinear", "nearestMipmapNearest" ]
        doc = "Specifies the minification filter mode for this texture."
    )
    
    token inputs:magFilter (
        allowedTokens = ["nearest", "linear"]
        doc = "Specifies the magnification filter mode for this texture."
    )

    float4 inputs:fallback = (0.0, 0.0, 0.0, 1.0) (
        doc = """Fallback value to be used when no texture is connected."""
        sdrMetadata = {
            token defaultInput = "1"
        }
    )
}

def Shader "HwFieldReader_1" (
    sdrMetadata = {
        token role = "field"
    }
    doc = """Samples from a usdVol FieldAsset."""
)
{
    uniform asset info:glslfx:sourceAsset = @./empty.glslfx@
    uniform token info:implementationSource = "sourceAsset"
    token inputs:fieldname = "" (
        connectability = "interfaceOnly"
        doc = """Name of the field to be fetched from volume
                 (e.g., FOO if the relationship from the volume to field is called field::FOO)."""
        sdrMetadata = {
            token fieldProperty = "1"
        }
    )
}

def Shader "HwFieldReader_float" ( 
    inherits = </HwFieldReader_1>
)
{
    uniform token info:id = "HwFieldReader_float"
    float inputs:fallback = 0 (
        doc = "Fallback value to be returned when fetch failed."
        sdrMetadata = {
            token defaultInput = "1"
        }
    )
    float outputs:result (
        sdrMetadata = {
            token swizzle = "x"
        }
    )
}

def Shader "HwFieldReader_float2" (
    inherits = </HwFieldReader_1>
)
{
    uniform token info:id = "HwFieldReader_float2"
    float2 inputs:fallback = (0, 0) (
        doc = "Fallback value to be returned when fetch failed."
        sdrMetadata = {
            token defaultInput = "1"
        }
    )
    float2 outputs:result (
        sdrMetadata = {
            token swizzle = "xy"
        }
    )
}

def Shader "HwFieldReader_float3" (
    inherits = </HwFieldReader_1>
)
{
    uniform token info:id = "HwFieldReader_float3"
    float3 inputs:fallback = (0, 0, 0) (
        doc = "Fallback value to be returned when fetch failed."
        sdrMetadata = {
            token defaultInput = "1"
        }
    )
    float3 outputs:result (
        sdrMetadata = {
            token swizzle = "xyz"
        }
    )
}

def Shader "HwPrimvar_1" (
    sdrMetadata = {
        token role = "primvar"
    }
)
{
    uniform token info:id = "HwPrimvar_1"
    uniform token info:implementationSource = "sourceAsset"

    # Add a dummy sourceAsset attribute with sourceType="glslfx", so that 
    # an entry gets created for this deprecated shader in the registry.
    uniform asset info:glslfx:sourceAsset = @./empty.glslfx@

    token inputs:varname = "" (
        sdrMetadata = {
            bool primvarProperty = 1
        }
        doc = """The name of the primvar. Note that on the gprim, this primvar
        must follow the standard UsdGeom primvar declaration.        
        Further note that this name should not contain the UsdGeom primvar
        namespace prefix.
        """
    )
}
