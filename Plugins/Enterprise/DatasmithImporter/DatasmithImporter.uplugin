{"FileVersion": 3, "Version": 1, "VersionName": "1.0", "FriendlyName": "Datasmith Importer", "Description": "Importer for Datasmith files.", "Category": "Importers", "CreatedBy": "Epic Games, Inc.", "CreatedByURL": "https://epicgames.com", "DocsURL": "https://docs.unrealengine.com/en-US/WorkingWithContent/Importing/Datasmith/", "MarketplaceURL": "", "SupportURL": "", "EnabledByDefault": false, "CanContainContent": false, "IsBetaVersion": false, "Installed": false, "Modules": [{"Name": "DatasmithExternalSource", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "DatasmithImporter", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "DatasmithTranslator", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "DatasmithNativeTranslator", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "DirectLinkExtension", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "DirectLinkExtensionEditor", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "ExternalSource", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "DirectLinkTest", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>"}], "Plugins": [{"Name": "DatasmithContent", "Enabled": true}, {"Name": "EditorScriptingUtilities", "Enabled": true}, {"Name": "DataPrepEditor", "Enabled": true, "TargetAllowList": ["Editor"]}, {"Name": "InterchangeEditor", "Enabled": true, "TargetAllowList": ["Editor"]}, {"Name": "VariantManager<PERSON><PERSON>nt", "Enabled": true}, {"Name": "VariantManager", "Enabled": true}, {"Name": "UdpMessaging", "Enabled": true}]}