{"FileVersion": 3, "FriendlyName": "Online Subsystem EOS", "Version": 1, "VersionName": "1.0", "Description": "Online Subsystem for Epic Online Services", "Category": "Online Platform", "CreatedBy": "Epic Games, Inc.", "CreatedByURL": "https://dev.epicgames.com/services", "EnabledByDefault": false, "IsBetaVersion": true, "Modules": [{"Name": "OnlineSubsystemEOS", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>", "PlatformAllowList": ["Win64", "<PERSON>", "Linux", "LinuxArm64", "Android"]}, {"Name": "OnlineSubsystemEOSPlus", "Type": "Runtime", "LoadingPhase": "None", "PlatformAllowList": ["Win64", "<PERSON>", "Linux", "LinuxArm64", "Android"]}], "Plugins": [{"Name": "OnlineSubsystem", "Enabled": true}, {"Name": "OnlineSubsystemUtils", "Enabled": true}, {"Name": "EOSShared", "Enabled": true}, {"Name": "EOSVoiceChat", "Enabled": true}, {"Name": "SocketSubsystemEOS", "Enabled": true}, {"Name": "EOSOverlayInputProvider", "Enabled": true}]}