// Copyright Epic Games, Inc. All Rights Reserved.

/*=============================================================================
	LumenRadianceCacheCommon.ush
=============================================================================*/

#pragma once

#include "LumenRadianceCacheInterpolation.ush"

FRadianceCacheCoverage GetRadianceCacheCoverageWithUncertainCoverage(float3 RayOrigin, float3 RayDirection, float ClipmapDitherRandom)
{
	FRadianceCacheCoverage Coverage;
	Coverage.bValid = false;
	Coverage.MinTraceDistanceBeforeInterpolation = 10000000.0f;

	uint ClipmapIndex = GetRadianceProbeClipmap(RayOrigin, ClipmapDitherRandom);

	if (ClipmapIndex < NumRadianceProbeClipmaps)
	{
		float3 ProbeCoordFloat = GetRadianceProbeCoordFloat(RayOrigin, ClipmapIndex);
		float3 CornerProbeCoordFloat = ProbeCoordFloat - .5f;
		int3 CornerProbeCoord = floor(CornerProbeCoordFloat);
		Coverage.bValid = true;

		UNROLL
		for (int Z = 0; Z < 2; Z++)
		{
			UNROLL
			for (int Y = 0; Y < 2; Y++)
			{
				UNROLL
				for (int X = 0; X < 2; X++)
				{
					int3 ProbeCoord = CornerProbeCoord + int3(X, Y, Z);
					uint ProbeIndex = GetProbeIndexFromIndirectionTexture(ProbeCoord, ClipmapIndex);

					if (ProbeIndex == INVALID_PROBE_INDEX)
					{
						Coverage.bValid = false;
					}
				}
			}
		}
		
		float CellOcclusionDistance = GetRadianceProbeClipmapCellSize(ClipmapIndex) * sqrt(3.0f);
		Coverage.MinTraceDistanceBeforeInterpolation = GetRadianceProbeTMin(ClipmapIndex) + CellOcclusionDistance;
	}

	return Coverage;
}
