// Copyright Epic Games, Inc. All Rights Reserved.

/*================================================================================================
	RayTracingDebugDefinitions.ush: used in ray tracing debug shaders and C++ code to define common constants
	!!! Changing this file requires recompilation of the engine !!!
=================================================================================================*/

#pragma once

#define RAY_TRACING_DEBUG_VIZ_RADIANCE					0 
#define RAY_TRACING_DEBUG_VIZ_WORLD_NORMAL				1 
#define RAY_TRACING_DEBUG_VIZ_BASE_COLOR				2 
#define RAY_TRACING_DEBUG_VIZ_DIFFUSE_COLOR				3 
#define RAY_TRACING_DEBUG_VIZ_SPECULAR_COLOR			4 
#define RAY_TRACING_DEBUG_VIZ_OPACITY					5 
#define RAY_TRACING_DEBUG_VIZ_METALLIC					6 
#define RAY_TRACING_DEBUG_VIZ_SPECULAR					7 
#define RAY_TRACING_DEBUG_VIZ_ROUGHNESS					8 
#define RAY_TRACING_DEBUG_VIZ_IOR						9 
#define RAY_TRACING_DEBUG_VIZ_SHADING_MODEL				10 
#define RAY_TRACING_DEBUG_VIZ_BLENDING_MODE				11  
#define RAY_TRACING_DEBUG_VIZ_LIGHTING_CHANNEL_MASK		12  
#define RAY_TRACING_DEBUG_VIZ_CUSTOM_DATA				13  
#define RAY_TRACING_DEBUG_VIZ_GBUFFER_AO				14  
#define RAY_TRACING_DEBUG_VIZ_INDIRECT_IRRADIANCE		15  
#define RAY_TRACING_DEBUG_VIZ_WORLD_POSITION			16  
#define RAY_TRACING_DEBUG_VIZ_HITKIND					17  
#define RAY_TRACING_DEBUG_VIZ_BARYCENTRICS				18  
#define RAY_TRACING_DEBUG_VIZ_PRIMARY_RAYS				19  
#define RAY_TRACING_DEBUG_VIZ_WORLD_TANGENT				20 
#define RAY_TRACING_DEBUG_VIZ_ANISOTROPY				21 
#define RAY_TRACING_DEBUG_VIZ_INSTANCES					22
#define RAY_TRACING_DEBUG_VIZ_PERFORMANCE				23 
#define RAY_TRACING_DEBUG_VIZ_TRIANGLES					24
#define RAY_TRACING_DEBUG_VIZ_FAR_FIELD					25
#define RAY_TRACING_DEBUG_VIZ_TRAVERSAL_NODE			26
#define RAY_TRACING_DEBUG_VIZ_TRAVERSAL_CLUSTER			27
#define RAY_TRACING_DEBUG_VIZ_TRAVERSAL_TRIANGLE		28
#define RAY_TRACING_DEBUG_VIZ_TRAVERSAL_ALL				29
#define RAY_TRACING_DEBUG_VIZ_TRAVERSAL_STATISTICS		30
#define RAY_TRACING_DEBUG_VIZ_DYNAMIC_INSTANCES			31
#define RAY_TRACING_DEBUG_VIZ_PROXY_TYPE				32
#define RAY_TRACING_DEBUG_VIZ_PICKER					33
#define RAY_TRACING_DEBUG_VIZ_INSTANCE_OVERLAP			34
#define RAY_TRACING_DEBUG_VIZ_TRIANGLE_HITCOUNT			35
#define RAY_TRACING_DEBUG_VIZ_HITCOUNT_PER_INSTANCE		36
#define RAY_TRACING_DEBUG_VIZ_LIGHT_GRID_COUNT			37
#define RAY_TRACING_DEBUG_VIZ_SUBSTRATE_DATA			38

#define RAY_TRACING_DEBUG_PICKER_DOMAIN_TRIANGLE		0
#define RAY_TRACING_DEBUG_PICKER_DOMAIN_INSTANCE		1
#define RAY_TRACING_DEBUG_PICKER_DOMAIN_SEGMENT			2

